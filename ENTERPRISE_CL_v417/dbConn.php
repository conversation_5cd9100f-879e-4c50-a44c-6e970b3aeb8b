
<?php
	include('config.php');

	// Conexión usando MySQLi para compatibilidad con MariaDB
	$mysql_connection = null;

	// Verificar si las funciones MySQL ya existen antes de declararlas
	if (!function_exists('mysql_connect')) {
		// Función mysql_connect compatible
		function mysql_connect($server, $user, $pass) {
			global $mysql_connection;

			// Separar host y puerto si están juntos
			$host_parts = explode(':', $server);
			$host = $host_parts[0];
			$port = isset($host_parts[1]) ? $host_parts[1] : 3306;

			$mysql_connection = new mysqli($host, $user, $pass, '', $port);

			if ($mysql_connection->connect_error) {
				die("Error de conexión: " . $mysql_connection->connect_error);
			}

			// Configurar charset para evitar problemas con MariaDB
			$mysql_connection->set_charset("utf8");

			return $mysql_connection;
		}
	}

	if (!function_exists('mysql_select_db')) {
		// Función mysql_select_db compatible
		function mysql_select_db($database, $connection = null) {
			global $mysql_connection;
			$conn = $connection ? $connection : $mysql_connection;
			return $conn->select_db($database);
		}
	}

	if (!function_exists('mysql_set_charset')) {
		// Función mysql_set_charset compatible
		function mysql_set_charset($charset, $connection = null) {
			global $mysql_connection;
			$conn = $connection ? $connection : $mysql_connection;
			return $conn->set_charset($charset);
		}
	}

	if (!function_exists('mysql_query')) {
		// Función mysql_query compatible
		function mysql_query($query, $connection = null) {
			global $mysql_connection;
			$conn = $connection ? $connection : $mysql_connection;
			return $conn->query($query);
		}
	}

	if (!function_exists('mysql_fetch_array')) {
		// Función mysql_fetch_array compatible
		function mysql_fetch_array($result, $result_type = MYSQLI_BOTH) {
			if ($result && is_object($result)) {
				return $result->fetch_array($result_type);
			}
			return false;
		}
	}

	if (!function_exists('mysql_num_rows')) {
		// Función mysql_num_rows compatible
		function mysql_num_rows($result) {
			if ($result && is_object($result)) {
				return $result->num_rows;
			}
			return 0;
		}
	}

	if (!function_exists('mysql_insert_id')) {
		// Función mysql_insert_id compatible
		function mysql_insert_id($connection = null) {
			global $mysql_connection;
			$conn = $connection ? $connection : $mysql_connection;
			return $conn->insert_id;
		}
	}

	if (!function_exists('mysql_error')) {
		// Función mysql_error compatible
		function mysql_error($connection = null) {
			global $mysql_connection;
			$conn = $connection ? $connection : $mysql_connection;
			return $conn->error;
		}
	}

	if (!function_exists('mysql_close')) {
		// Función mysql_close compatible
		function mysql_close($connection = null) {
			global $mysql_connection;
			$conn = $connection ? $connection : $mysql_connection;
			return $conn->close();
		}
	}

	// Inicializar conexión automáticamente
	// Usar MySQLi directamente para evitar problemas de charset con MariaDB
	$host_parts = explode(':', SERVERDB);
	$host = $host_parts[0];
	$port = isset($host_parts[1]) ? $host_parts[1] : 3306;

	// Crear conexión MySQLi global para compatibilidad
	$mysql_connection = new mysqli($host, USERDB, PASSDB, DB, $port);

	if ($mysql_connection->connect_error) {
		die("Error de conexión: " . $mysql_connection->connect_error);
	}

	// Configurar charset inmediatamente después de conectar
	// Usar latin1 inicialmente para evitar el problema del charset (255)
	@mysql_set_charset('latin1', $connection);

	// Seleccionar base de datos
	if (!mysql_select_db(DB, $connection)) {
		die("Error seleccionando base de datos: " . mysql_error());
	}

	// Ahora configurar UTF-8 mediante consultas SQL
	mysql_query("SET NAMES utf8", $connection);
	mysql_query("SET CHARACTER SET utf8", $connection);
	mysql_query("SET character_set_connection=utf8", $connection);
	mysql_query("SET character_set_client=utf8", $connection);
	mysql_query("SET character_set_results=utf8", $connection);

	// Redefinir funciones MySQL para usar la conexión MySQLi
	if (function_exists('mysql_connect')) {
		// Las funciones nativas existen pero causan problemas con MariaDB
		// Crear aliases que usen MySQLi internamente
		function mysql_connect_override($server, $user, $pass) {
			global $mysql_connection;
			return $mysql_connection;
		}

		function mysql_select_db_override($database, $connection = null) {
			global $mysql_connection;
			return true; // Ya seleccionada en la conexión
		}

		function mysql_set_charset_override($charset, $connection = null) {
			global $mysql_connection;
			return $mysql_connection->set_charset($charset);
		}
	}

	// Mantener PDO para compatibilidad con código nuevo
	try {
		$dsn = "mysql:host=".$host.";port=".$port.";dbname=".DB.";charset=utf8";
		$options = [
			PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
			PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8 COLLATE utf8_general_ci"
		];
		$dbh = new PDO($dsn, USERDB, PASSDB, $options);
	} catch (PDOException $e) {
		die("Error PDO: " . $e->getMessage());
	}

	if (!isset($_SESSION)) { session_start(); }
?>
