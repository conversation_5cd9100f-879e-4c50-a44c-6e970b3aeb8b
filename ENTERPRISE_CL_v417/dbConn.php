<?php
	include('config.php');

	// Configuración PDO para MariaDB con charset UTF-8
	try {
		$dsn = "mysql:host=".SERVERDB.";dbname=".DB.";charset=utf8mb4";
		$options = [
			PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
			PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
			PDO::ATTR_EMULATE_PREPARES => false,
			PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
		];

		$dbh = new PDO($dsn, USERDB, PASSDB, $options);

		// Mantener compatibilidad con código legacy que pueda usar estas funciones
		function mysql_connect($server, $user, $pass) {
			// Esta función ya no es necesaria, la conexión se maneja con PDO
			return true;
		}

		function mysql_select_db($db) {
			// Esta función ya no es necesaria, la base de datos se selecciona en PDO
			return true;
		}

		function mysql_set_charset($charset) {
			// Esta función ya no es necesaria, el charset se configura en PDO
			return true;
		}

		function mysql_error() {
			global $dbh;
			$errorInfo = $dbh->errorInfo();
			return isset($errorInfo[2]) ? $errorInfo[2] : '';
		}

	} catch (PDOException $e) {
		die("Error de conexión a la base de datos: " . $e->getMessage());
	}

	if (!isset($_SESSION)) { session_start(); }
?>
