<?php
	include('config.php');

	// Conexión usando MySQLi para compatibilidad con MariaDB
	$mysql_connection = null;

	// Función mysql_connect compatible
	function mysql_connect($server, $user, $pass) {
		global $mysql_connection;

		// Separar host y puerto si están juntos
		$host_parts = explode(':', $server);
		$host = $host_parts[0];
		$port = isset($host_parts[1]) ? $host_parts[1] : 3306;

		$mysql_connection = new mysqli($host, $user, $pass, '', $port);

		if ($mysql_connection->connect_error) {
			die("Error de conexión: " . $mysql_connection->connect_error);
		}

		// Configurar charset para evitar problemas con MariaDB
		$mysql_connection->set_charset("utf8");

		return $mysql_connection;
	}

	// Función mysql_select_db compatible
	function mysql_select_db($database, $connection = null) {
		global $mysql_connection;
		$conn = $connection ? $connection : $mysql_connection;
		return $conn->select_db($database);
	}

	// Función mysql_set_charset compatible
	function mysql_set_charset($charset, $connection = null) {
		global $mysql_connection;
		$conn = $connection ? $connection : $mysql_connection;
		return $conn->set_charset($charset);
	}

	// Función mysql_query compatible
	function mysql_query($query, $connection = null) {
		global $mysql_connection;
		$conn = $connection ? $connection : $mysql_connection;
		return $conn->query($query);
	}

	// Función mysql_fetch_array compatible
	function mysql_fetch_array($result, $result_type = MYSQLI_BOTH) {
		if ($result && is_object($result)) {
			return $result->fetch_array($result_type);
		}
		return false;
	}

	// Función mysql_num_rows compatible
	function mysql_num_rows($result) {
		if ($result && is_object($result)) {
			return $result->num_rows;
		}
		return 0;
	}

	// Función mysql_insert_id compatible
	function mysql_insert_id($connection = null) {
		global $mysql_connection;
		$conn = $connection ? $connection : $mysql_connection;
		return $conn->insert_id;
	}

	// Función mysql_error compatible
	function mysql_error($connection = null) {
		global $mysql_connection;
		$conn = $connection ? $connection : $mysql_connection;
		return $conn->error;
	}

	// Función mysql_close compatible
	function mysql_close($connection = null) {
		global $mysql_connection;
		$conn = $connection ? $connection : $mysql_connection;
		return $conn->close();
	}

	// Inicializar conexión automáticamente
	mysql_connect(SERVERDB, USERDB, PASSDB);
	mysql_set_charset('utf8');
	mysql_select_db(DB) or die(mysql_error());

	// Mantener PDO para compatibilidad con código nuevo
	$dbh = new PDO("mysql:dbname=".DB.";host=".SERVERDB.";charset=utf8", USERDB, PASSDB);

	if (!isset($_SESSION)) { session_start(); }
?>
